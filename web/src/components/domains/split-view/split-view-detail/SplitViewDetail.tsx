import * as React from 'react';
import { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import {
  Text, Skeleton, Button, Provider, teamsTheme, PaperclipIcon, Attachment, EmojiIcon,
} from '@fluentui/react-northstar';
import Scrollbars from 'react-custom-scrollbars-2';
import useFixedHeightCalcBehavior from '../../../../hooks/behaviors/useFixedHeightCalcBehavior';
import useSwipeBackBehavior from '../../../../hooks/behaviors/useSwipeBackBehavior';
import { ValueOf } from '../../../../utilities/type';
import { mergedClassName } from '../../../../utilities/commonFunction';
import { makeChatSanitizedInnerHtml, makeMailSanitizedInnerHtml, makeSpoSanitizedInnerHtml } from '../../../../utilities/sanitize';
import { blankToDash } from '../../../../utilities/text';
import { DateFormatTemplate, toDateString } from '../../../../utilities/date';
import { isEnterKeydownOrClick, ReactEvent } from '../../../../utilities/event';
import { getAttachmentIcon } from '../../../../utilities/icons';
import { convertSplitViewItemToBookmarkItem, isChatProperties } from '../../../../utilities/transform';
import ModalCardTop from '../../../commons/molecules/modal-card-top/ModalCardTop';
import NoticeBox from '../../../commons/molecules/notice-box/NoticeBox';
import SplitViewDetailHeader, { SplitViewDetailHeaderView } from '../split-view-detail-header/SplitViewDetailHeader';
import { ISplitViewDetail } from '../types/ISplitViewDetail';
import { IDetailAttachment } from '../types/IDetailAttachment';
import { IChatProperties, IMailProperties } from '../../../../types/ISearchResult';
import { getDisplayAttachments } from './SplitViewDetail.functions';
import { ISplitViewListSingle } from '../types/ISplitViewListSingle';
import ReactionList from '../../../commons/molecules/reaction-list/ReactionList';
import { IChatReaction } from '../../../../types/IChatResponse';
import { IGraphUser } from '../../../../types/IGraphUser';
import { IReactionSingleProps } from '../../../commons/atoms/reaction-single/ReactionSingle';
import { DataSourceKind } from '../../../../types/DataSourceKind';

// CSS
import './SplitViewDetail.scss';

/**
 * 表示の状態
 * @property LOADING 表示中・ローディング状態
 * @property DEFAULT 表示中・デフォルト状態
 * @property ERROR 表示中・エラー状態
 */
export const SplitViewDetailView = {
  LOADING: 'loading',
  DEFAULT: 'default',
  ERROR: 'error',
};
export type SplitViewDetailViewType = ValueOf<typeof SplitViewDetailView>;

export const SplitViewDetailMessage = {
  BLANK: '',
  NO_ITEMS: '表示できる項目がありませんでした。',
  API_REQUEST_FAIL: '通信エラーが発生しました。',
  TOO_MANY_REQUEST: '一度に大量のリクエストが送信されたました。しばらくしてからもう一度お試しください。',
  UNAUTHORIZED: '認証されていないか、取得する権限がありません。管理者に確認してください。',
  UNAVAILABLE: 'この文書は非公開、掲示期限切れ、削除済み、あるいは閲覧権限がありません。',
  NOT_SELECTED: '左側から閲覧するアイテムを選択してください',
  NO_BOOKMARKS: '左側から閲覧するアイテムを選択してください',
};
export type SplitViewDetailMessageType = ValueOf<typeof SplitViewDetailMessage>;

export interface ISplitViewDetailProps {
  className?: string | null;
  open?: boolean | null;
  view?: SplitViewDetailViewType,
  message?: SplitViewDetailMessageType,
  detail?: ISplitViewDetail,
  isBookmarked?: boolean;
  onClickAttachment?: (attachment: IDetailAttachment) => void,
  onClickBookmark?: (item: ISplitViewListSingle, toBe: boolean) => void,
  onClickAltLink?: (item: ISplitViewDetail | undefined) => void,
  onSwipingBack?: (n: number) => void,
  onClose?: () => void,
  onChangeDetail?: () => void,
  $bodyRef?: React.RefObject<HTMLDivElement> | null,
  fetchDisplayNames?: (reactions: IChatReaction[]) => Promise<IGraphUser[] | void>,
  groupByReactions?: (users: IGraphUser[], reactions: IChatReaction[]) => IReactionSingleProps[],
  replyOptionsProp?: { replyToId: string | null; teamChatType: string | null };
}

const SplitViewDetail: React.FC<ISplitViewDetailProps> = (props) => {
  const {
    className,
    open,
    view,
    message,
    detail,
    isBookmarked,
    onClickAttachment,
    onClickBookmark,
    onClickAltLink,
    onClose,
    onSwipingBack,
    onChangeDetail,
    $bodyRef,
    fetchDisplayNames,
    groupByReactions,
    replyOptionsProp,
  } = props;

  const [isLoadingLink, setIsLoadingLink] = useState(true);
  useEffect(() => {
    if (!detail?.properties || !replyOptionsProp) {
      setIsLoadingLink(true);
      return;
    }
    const { messageType } = detail.properties as IChatProperties;
    const { replyToId, teamChatType } = replyOptionsProp;
    // chatの場合、またはteamのルートの場合は読み込み完了
    if (messageType === 'chat' || (messageType === 'team' && teamChatType === 'root')) {
      setIsLoadingLink(false);
      return;
    }
    // teamでreplyの場合、replyToIdがないなら読み込み継続、あれば値をセットして完了
    if (messageType === 'team' && teamChatType === 'reply') {
      if (!replyToId) {
        setIsLoadingLink(true);
      } else {
        detail.properties = { ...detail.properties, replyToId };
        setIsLoadingLink(false);
      }
    }
  }, [replyOptionsProp, detail]);

  const isLoading = React.useMemo(() => (
    view === SplitViewDetailView.LOADING
  ), [view]);

  const isDefault = React.useMemo(() => (
    view === SplitViewDetailView.DEFAULT
  ), [view]);

  const isError = React.useMemo(() => (
    view === SplitViewDetailView.ERROR
  ), [view]);

  // 閉じるボタンクリックのコールバック
  const handleOnClose = React.useCallback(() => {
    if (!onClose) return;
    onClose();
  }, [onClose]);

  // 添付ファイル項目クリックのコールバック
  const handleOnClickAttachment = React.useCallback(
    (attachment: IDetailAttachment, e: ReactEvent) => {
      e.preventDefault();
      if (!onClickAttachment) return;
      if (!isEnterKeydownOrClick(e)) return;
      onClickAttachment(attachment);
    }, [onClickAttachment],
  );

  // 表示する添付ファイルデータ
  const displayAttachments = React.useMemo(
    () => getDisplayAttachments(detail),
    [detail],
  );

  // 添付ファイル有無
  const hasAttachments = React.useMemo(
    () => (displayAttachments?.length ?? 0) > 0,
    [displayAttachments],
  );

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const isChat = React.useMemo(() => (
    detail?.kind === DataSourceKind.Chat
  ), [detail?.kind]);
  const chatProperties = React.useMemo(() => (
    (isChatProperties(detail?.properties) ? detail?.properties ?? {} : {}) as IChatProperties
  ), [detail]);
  const hasReactions = React.useMemo(
    () => (chatProperties?.reactions ?? []).length > 0,
    [chatProperties],
  );

  // お気に入りsplit-view-detail-scrollボタンクリックのコールバック
  const handleOnClickBookmark = React.useCallback((toBe: boolean) => {
    if (!onClickBookmark) return;
    if (!detail?.properties) return;
    const detailToBookmark = detail as Required<ISplitViewDetail>;
    onClickBookmark(
      convertSplitViewItemToBookmarkItem(
        {
          ...detailToBookmark,
          properties: {
            ...detailToBookmark.properties,
            hasAttachments,
          },
        },
      ),
      toBe,
    );
  }, [onClickBookmark, detail, hasAttachments]);

  // スクロールバーの参照
  const scrollbarsRef = React.useRef<Scrollbars>(null);

  // DEFAULT以外からLOADINGへ変化したときにスクロール位置をリセット
  // reset the scroll position if the view changed to be LOADING from DEFAULT
  const viewCache = React.useRef<SplitViewDetailViewType>(SplitViewDetailView.LOADING);
  React.useEffect(() => {
    if (!view) return;
    if (viewCache.current !== SplitViewDetailView.LOADING
      && view === SplitViewDetailView.LOADING) {
      if (scrollbarsRef.current) {
        scrollbarsRef.current.scrollTop(0);
      }
    }
    viewCache.current = view;
  }, [view]);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const attachmentsRef = React.useRef<any>(undefined);
  // 添付ファイルセクションへのスクロール
  const handleOnClickAttachmentAnchor = React.useCallback(() => {
    if (!attachmentsRef.current) return;

    (attachmentsRef.current as HTMLElement).scrollIntoView({
      behavior: 'smooth',
    });
  }, []);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const reactionsRef = React.useRef<any>(undefined);
  // リアクションセクションへのスクロール
  const handleOnClickReactionsAnchor = React.useCallback(() => {
    if (!reactionsRef.current) return;

    (reactionsRef.current as HTMLElement).scrollIntoView({
      behavior: 'smooth',
    });
  }, []);

  const handleOnClickAltLink = React.useCallback(() => {
    if (onClickAltLink) onClickAltLink(detail);
  }, [detail, onClickAltLink]);

  function stripBody(content: string) {
    return content.replace((/[\s\S]*<body.*?>([\s\S]*)<\/body>[\s\S]*/), '$1');
  }

  function makeMailBody(body: string, properties: IMailProperties): { __html: string } {
    const content = properties?.contentType === 'html' ? stripBody(body) : body;
    return makeMailSanitizedInnerHtml(content);
  }
  // makeChatSanitizedInnerHtmlを渡すだけの関数だが可読性が下がるため保持
  function makeChatBody(body: string): { __html: string } {

    return makeChatSanitizedInnerHtml(body);
  }

  function getBodyContent(d?: ISplitViewDetail): { __html: string } {
    if (!d) return { __html: '' };
    // TODO: 結構たくさん現れるのでこういうの種類ごとにまとめてクラスにする？
    switch (d.kind) {
      case 'SPO':
        return makeSpoSanitizedInnerHtml(d.body);
      case 'Mail':
        return makeMailBody(d.body ?? '', d.properties as IMailProperties);
      case 'Chat':
        return makeChatBody(d.body ?? '');
      default:
        return { __html: '' };
    }
  }

  // 本文部分のinnerHTML
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const bodyMarkup = React.useMemo(() => getBodyContent(detail), [detail]);

  // header固定表示の判定
  // コンポーネントの高さの40%を境に判定
  const [
    // コンポーネントのルート要素への参照
    $componentRef,
    // 固定時のヘッダー要素への参照
    $fixedHeaderRef,
    // 非固定時のヘッダー要素への参照
    $staticHeaderRef,
    isFixedHeader,
  ] = useFixedHeightCalcBehavior(0.4, isDefault);

  // スクロールエリアのクラス名
  const scrollbarClassNames = React.useMemo(() => {
    const isLoadingClass = mergedClassName('split-view-detail-scroll', isLoading ? 'is-loading' : undefined);
    return mergedClassName(isLoadingClass, isError ? 'is-error' : undefined);
  }, [isError, isLoading]);

  // swipe-back feature
  const [
    touchLength, onTouchStart, onTouchMove, onTouchEnd, touchInfo,
  ] = useSwipeBackBehavior(10, onClose);
  React.useEffect(() => {
    if (onSwipingBack) onSwipingBack(touchLength);
  }, [touchLength, onSwipingBack]);

  // 本文の描画が変更されたときにonChangeDetailを実行
  React.useEffect(() => {
    // TODO:ログが２回出ているので直す
    console.log('Pass');
    if (!detail?.id || !onChangeDetail) return;
    onChangeDetail();
  }, [detail, onChangeDetail, isFixedHeader]);

  // スクロール量が条件を満たしたらスワイプ開始する
  // start swipe-back effect when the scroll amount fills the condition
  const handleOnTouchStartScrollbarWrapper = React.useCallback(
    (e: React.SyntheticEvent<HTMLElement, TouchEvent | MouseEvent>) => {
      if (!scrollbarsRef.current) return;
      if (scrollbarsRef.current.getValues().scrollTop <= 0) {
        onTouchStart(e);
      }
    }, [onTouchStart],
  );

  // スクロール量が条件を満たしたらスワイプ開始する
  // start swipe-back effect when the scroll amount fills the condition
  const handleOnTouchMoveScrollbarWrapper = React.useCallback(
    (e: React.SyntheticEvent<HTMLElement, TouchEvent | MouseEvent>) => {
      const isNotCapturingAndNoScrollLength = !touchInfo.current.isCapturing
        && scrollbarsRef.current
        && scrollbarsRef.current.getValues().scrollTop <= 0;

      if (isNotCapturingAndNoScrollLength) {
        onTouchStart(e);
      } else {
        onTouchMove(e);
      }
    }, [onTouchMove, touchInfo, onTouchStart],
  );

  // クラス名
  const rootClassName = React.useMemo(() => {
    const base = mergedClassName('split-view-detail', open ? 'is-open' : '');
    return mergedClassName(base, className ?? '');
  }, [open, className]);

  const contentClassName = mergedClassName('split-view-detail-body-content', (detail?.kind ?? '').toLowerCase());

  return (
    <div
      ref={$componentRef}
      className={rootClassName}
      tabIndex={0}
      role="slider"
      aria-valuenow={0}
    >
      {/* 閉じるボタン */}
      <div className="split-view-detail-close">
        <ModalCardTop
          onClickClose={handleOnClose}
          onClickBookmark={handleOnClickBookmark}
          isBookmarked={isBookmarked}
          showBookmark={isDefault}
          onTouchStart={onTouchStart}
          onTouchMove={onTouchMove}
          onTouchEnd={onTouchEnd}
          onTouchCancel={onTouchEnd}
        />
      </div>
      <div className="split-view-detail-inner">

        {/* ヘッダー(固定時) */}
        {/* header (on fixed) */}
        {!isError && isFixedHeader && (
          <div ref={$fixedHeaderRef} className="split-view-detail-header-container is-fixed">
            <div className="split-view-detail-column">
              {/* もう1箇所同じコンポーネントが参照されているので、パラメータを変更するときは併せて修正する */}
              {/* we are using the two same header to switching fixed/not-fixed layout */}
              {/* please modify both of them on modify */}
              <SplitViewDetailHeader
                view={
                  isDefault
                    ? SplitViewDetailHeaderView.DEFAULT
                    : SplitViewDetailHeaderView.LOADING
                }
                detail={detail}
                useAttachmentAnchor={hasAttachments}
                isBookmarked={isBookmarked}
                // me={me}
                onClickAttachmentAnchor={handleOnClickAttachmentAnchor}
                onClickReactionsAnchor={handleOnClickReactionsAnchor}
                onClickBookmark={handleOnClickBookmark}
              />
            </div>
          </div>
        )}

        <div
          className="split-view-scrollbar-wrapper"
          onTouchStart={handleOnTouchStartScrollbarWrapper}
          onTouchMove={handleOnTouchMoveScrollbarWrapper}
          onTouchEnd={onTouchEnd}
          onTouchCancel={onTouchEnd}
        >
          <Scrollbars
            ref={scrollbarsRef}
            autoHide
            className={scrollbarClassNames}
          >
            {/* エラー */}
            {isError && (
              <NoticeBox
                className="split-view-detail-message"
                message={message}
                useIcon={message !== SplitViewDetailMessage.BLANK}
              />
            )}

            <div className="split-view-detail-main-container">
              {/* ヘッダー(非固定時) */}
              {/* header (not-fixed) */}
              {!isError && !isFixedHeader && (
                <div ref={$staticHeaderRef} className="split-view-detail-header-container">
                  <div className="split-view-detail-column">
                    {/* もう1箇所同じコンポーネントが参照されているので、パラメータを変更するときは併せて修正する */}
                    {/* we are using the two same header to switching fixed/not-fixed layout */}
                    {/* please modify both of them on modify */}
                    <SplitViewDetailHeader
                      view={
                        isDefault
                          ? SplitViewDetailHeaderView.DEFAULT
                          : SplitViewDetailHeaderView.LOADING
                      }
                      detail={detail}
                      useAttachmentAnchor={hasAttachments}
                      isBookmarked={isBookmarked}
                      onClickAttachmentAnchor={handleOnClickAttachmentAnchor}
                      onClickReactionsAnchor={handleOnClickReactionsAnchor}
                      onClickBookmark={handleOnClickBookmark}
                    />
                  </div>
                </div>
              )}

              {/* body content */}
              {!isError && (
                // put Provider to force the content in light theme even in dark mode
                <Provider theme={teamsTheme}>
                  <div className="split-view-detail-main split-view-detail-column">

                    {/* 内容 */}
                    <div className="split-view-detail-body">

                      {/* 通常表示 */}
                      {isDefault && (
                        <>
                          {/* アイテム詳細フォームへのリンク */}
                          {detail?.kind === 'SPO' && (
                            <div className="split-view-detail-item-link">
                              <Button
                                className="split-view-detail-item-link-button"
                                content="SharePointで開く"
                                onClick={handleOnClickAltLink}
                                size="small"
                                text
                                primary
                              />
                            </div>
                          )}
                          {detail?.kind === 'Mail' && (
                            <div className="split-view-detail-item-link">
                              <Button
                                className="split-view-detail-item-link-button"
                                content="Outlookで開く"
                                onClick={handleOnClickAltLink}
                                size="small"
                                text
                                primary
                              />
                            </div>
                          )}
                          {detail?.kind === 'Chat' && (
                            <div className="split-view-detail-item-link">
                              {isLoadingLink ? (
                                <div>Loading...</div>
                              ) : (
                                <Button
                                  className="split-view-detail-item-link-button"
                                  content="チーム・チャットで開く"
                                  onClick={handleOnClickAltLink}
                                  size="small"
                                  text
                                  primary
                                />
                              )}
                            </div>
                          )}
                          {detail?.kind === 'Chat' && chatProperties.subject && (
                            <div>
                              <p className="chat-subject">{chatProperties.subject}</p>
                            </div>
                          )}
                          {/* SharePoint Onlineのコンテンツを直接スタイル付きで表示するために必要 */}
                          {/* eslint-disable react/no-danger */}
                          <div
                            ref={$bodyRef || undefined}
                            className={contentClassName}
                            dangerouslySetInnerHTML={bodyMarkup}
                          />
                          {/* eslint-enable react/no-danger */}
                        </>
                      )}

                      {/* ローディング */}
                      {isLoading && (
                        <Skeleton
                          className="split-view-detail-skeleton"
                          animation="wave"
                        >
                          <Skeleton.Text className="split-view-detail-body-content" />
                          <Skeleton.Text className="split-view-detail-body-content" />
                          <Skeleton.Text className="split-view-detail-body-content" />
                          <Skeleton.Text className="split-view-detail-body-content" />
                        </Skeleton>
                      )}
                    </div>

                    {/* 掲示期限 */}
                    {detail?.kind === 'SPO' && (
                      <div className="split-view-detail-date">
                        <Text content={`掲示期限：${blankToDash(toDateString(
                          detail?.expiredDate, DateFormatTemplate.YearMonthDay,
                        ))}`}
                        />
                      </div>
                    )}

                    {/* 添付ファイル一覧 */}
                    {hasAttachments && (
                      <div ref={attachmentsRef} className="split-view-detail-attachments">
                        <div className="split-view-detail-attachments-head">
                          <PaperclipIcon outline size="medium" />
                          <Text content="添付ファイル" size="medium" />
                        </div>

                        {/* 通常表示 */}
                        {isDefault && (
                          <ul className="split-view-detail-attachments-items">
                            {displayAttachments.map((attachment, i) => (
                              <li key={attachment.title ?? i} className="split-view-detail-attachments-item">
                                <Attachment
                                  icon={getAttachmentIcon(attachment.icon)}
                                  className="split-view-detail-attachment"
                                  onClick={(e) => handleOnClickAttachment(attachment, e)}
                                  header={attachment.title}
                                />
                              </li>
                            ))}
                          </ul>
                        )}

                        {/* ローディング */}
                        {isLoading && (
                          <Skeleton
                            className="split-view-detail-skeleton"
                            animation="wave"
                          >
                            <Skeleton.Shape className="split-view-detail-attachment" />
                          </Skeleton>
                        )}
                      </div>
                    )}

                    {/* リアクション一覧 */}
                    {hasReactions && (
                      <>
                        <div ref={reactionsRef} className="split-view-detail-reactions-head">
                          <EmojiIcon outline size="medium" />
                          <Text content="リアクション" size="medium" />
                        </div>
                        <div className="split-view-detail-reactions">
                          {/* 通常表示 */}
                          {isDefault && (
                            <ReactionList
                              reactions={chatProperties.reactions ?? []}
                              fetchDisplayNames={fetchDisplayNames}
                              groupByReactions={groupByReactions}
                            />
                          )}

                          {/* ローディング */}
                          {isLoading && (
                            <Skeleton
                              className="split-view-detail-skeleton"
                              animation="wave"
                            >
                              <Skeleton.Shape className="split-view-detail-attachment" />
                            </Skeleton>
                          )}
                        </div>
                      </>
                    )}
                  </div>
                </Provider>
              )}
            </div>

          </Scrollbars>
        </div>
      </div>
    </div>
  );
};

SplitViewDetail.propTypes = {
  className: PropTypes.string,
  open: PropTypes.bool,
  view: PropTypes.string,
  // eslint-disable-next-line react/forbid-prop-types
  detail: PropTypes.any,
  isBookmarked: PropTypes.bool,
  onClickAttachment: PropTypes.func,
  onClickBookmark: PropTypes.func,
  onClickAltLink: PropTypes.func,
  onClose: PropTypes.func,
  onChangeDetail: PropTypes.func,
  onSwipingBack: PropTypes.func,
  // RefオブジェクトのPropTypes指定方法が不明
  // eslint-disable-next-line react/forbid-prop-types
  $bodyRef: PropTypes.any,
};

SplitViewDetail.defaultProps = {
  className: undefined,
  open: undefined,
  view: SplitViewDetailView.LOADING,
  detail: undefined,
  isBookmarked: false,
  onClickAttachment: undefined,
  onClickBookmark: undefined,
  onClickAltLink: undefined,
  onClose: undefined,
  onChangeDetail: undefined,
  onSwipingBack: undefined,
  $bodyRef: undefined,
  message: SplitViewDetailMessage.BLANK,
  fetchDisplayNames: undefined,
  groupByReactions: undefined,
  replyOptionsProp: { replyToId: null, teamChatType: null },
};

export default React.memo(SplitViewDetail);
